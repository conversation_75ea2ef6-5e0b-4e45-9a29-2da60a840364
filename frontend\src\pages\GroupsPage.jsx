import React, { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Alert } from '@/components/ui/alert'
import { Avatar } from '@/components/ui/avatar'
import {
  Search,
  RefreshCw,
  UserPlus,
  Users,
  LogOut,
} from 'lucide-react'

import { useWhatsAppStore } from '../stores/whatsappStore'

const GroupsPage = () => {
  const {
    isReady,
    isAuthenticated,
    chats,
    isLoadingChats,
    loadChats,
  } = useWhatsAppStore()

  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    if (isReady && chats.length === 0) {
      loadChats()
    }
  }, [isReady, chats.length, loadChats])

  // Filter only groups
  const groups = chats.filter(chat => chat.isGroup)
  const filteredGroups = groups.filter(group =>
    group.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    group.id.includes(searchTerm)
  )

  if (!isAuthenticated) {
    return (
      <div className="space-y-6">
        <Alert className="border-yellow-200 bg-yellow-50 text-yellow-800">
          WhatsApp is not authenticated. Please scan the QR code on the dashboard first.
        </Alert>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Groups</h1>
        <p className="text-muted-foreground">
          Manage your WhatsApp groups
        </p>
      </div>

      {/* Actions */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search groups..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={loadChats}
            disabled={isLoadingChats}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
          >
            <UserPlus className="h-4 w-4 mr-2" />
            Create Group
          </Button>
        </div>
      </div>

      {/* Groups List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Groups ({filteredGroups.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoadingChats ? (
            <div className="flex justify-center p-8">
              <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent" />
            </div>
          ) : filteredGroups.length > 0 ? (
            <div className="space-y-4">
              {filteredGroups.map((group, index) => (
                <div
                  key={group.id}
                  className={cn(
                    "flex items-center space-x-4 p-4 rounded-lg border",
                    index < filteredGroups.length - 1 && "border-b"
                  )}
                >
                  <Avatar className="h-12 w-12">
                    <div className="flex h-full w-full items-center justify-center bg-secondary text-secondary-foreground">
                      <Users className="h-6 w-6" />
                    </div>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <h3 className="text-sm font-medium truncate">
                      {group.name || 'Unnamed Group'}
                    </h3>
                    <div className="flex items-center space-x-2 mt-1">
                      <p className="text-sm text-muted-foreground truncate">
                        {group.id}
                      </p>
                      {group.unreadCount > 0 && (
                        <Badge variant="default">
                          {group.unreadCount} unread
                        </Badge>
                      )}
                      {group.archived && (
                        <Badge variant="outline">Archived</Badge>
                      )}
                      {group.pinned && (
                        <Badge variant="secondary">Pinned</Badge>
                      )}
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm">
                      <LogOut className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex items-center justify-center p-8 text-muted-foreground">
              <div className="text-center">
                <Users className="h-16 w-16 mx-auto mb-4 opacity-50" />
                <p className="text-lg">
                  {searchTerm ? 'No groups found matching your search' : 'No groups available'}
                </p>
                {!searchTerm && (
                  <p className="text-sm mt-2">
                    Groups will appear here once you join or create them
                  </p>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default GroupsPage
